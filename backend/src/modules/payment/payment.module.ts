import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { Order, Payment, Refund } from './entities';
import { OrderService } from './services/order.service';
import { StripeService } from './services/stripe.service';
import { PaymentController } from './controllers/payment.controller';
import { CourseModule } from '../course/course.module';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, Payment, Refund]),
    ConfigModule,
    CourseModule,
    SubscriptionModule,
  ],
  controllers: [PaymentController],
  providers: [
    OrderService,
    StripeService,
  ],
  exports: [OrderService, StripeService],
})
export class PaymentModule {}