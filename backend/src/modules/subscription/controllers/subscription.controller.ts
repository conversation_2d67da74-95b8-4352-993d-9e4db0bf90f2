import { Controller, Get, UseGuards, Request, Post, Body, HttpException, HttpStatus, Put, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { SubscriptionService } from '../services/subscription.service';
import { SubscriptionPlanService } from '../services/subscription-plan.service';
import { CreateSubscriptionDto, UpdateSubscriptionDto, SubscriptionResponseDto, SubscriptionUsageDto, CancelSubscriptionDto } from '../dto/subscription.dto';
import { Request as ExpressRequest } from 'express';
import { SubscriptionStatus } from '../entities/subscription.entity';

@ApiTags('订阅')
@Controller('subscription')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly subscriptionPlanService: SubscriptionPlanService
  ) {}

  @Get('current')
  @ApiOperation({ summary: '获取当前订阅信息' })
  async getCurrentSubscription(@Request() req: ExpressRequest) {
    const userId = (req.user as any).id;
    return this.subscriptionService.getCurrentSubscription(userId);
  }

  @Get('history')
  @ApiOperation({ summary: '获取订阅历史' })
  async getSubscriptionHistory(@Request() req: ExpressRequest) {
    const userId = (req.user as any).id;
    return this.subscriptionService.getSubscriptionHistory(userId);
  }

  @Post('subscribe')
  @ApiOperation({ summary: '创建订阅' })
  async createSubscription(
    @Request() req: ExpressRequest,
    @Body() createSubscriptionDto: CreateSubscriptionDto
  ): Promise<SubscriptionResponseDto> {
    const userId = (req.user as any).id;
    return this.subscriptionService.createSubscriptionFromPlan(userId, createSubscriptionDto);
  }

  @Get('usage')
  @ApiOperation({ summary: '获取订阅使用情况' })
  async getSubscriptionUsage(@Request() req: ExpressRequest): Promise<SubscriptionUsageDto> {
    const userId = (req.user as any).id;
    return this.subscriptionService.getSubscriptionUsage(userId);
  }

  @Put('cancel')
  @ApiOperation({ summary: '取消订阅' })
  async cancelSubscription(
    @Request() req: ExpressRequest,
    @Body() cancelSubscriptionDto: CancelSubscriptionDto
  ): Promise<SubscriptionResponseDto> {
    const userId = (req.user as any).id;
    return this.subscriptionService.cancelSubscription(userId, cancelSubscriptionDto);
  }

  @Put('reactivate')
  @ApiOperation({ summary: '重新激活订阅' })
  async reactivateSubscription(@Request() req: ExpressRequest): Promise<SubscriptionResponseDto> {
    const userId = (req.user as any).id;
    return this.subscriptionService.reactivateSubscription(userId);
  }

  @Post('activate-trial')
  @ApiOperation({ summary: '激活试用期订阅' })
  async activateTrialSubscription(
    @Request() req: ExpressRequest,
    @Body() body: { subscriptionId: string }
  ): Promise<{ success: boolean; message: string }> {
    const userId = (req.user as any).id;
    
    // 验证订阅是否属于当前用户
    const subscription = await this.subscriptionService.findById(body.subscriptionId);
    if (!subscription || subscription.userId !== userId) {
      throw new HttpException('Subscription not found or not authorized', HttpStatus.NOT_FOUND);
    }

    // 激活试用期订阅
    await this.subscriptionService.updateSubscription(body.subscriptionId, {
      status: SubscriptionStatus.ACTIVE,
      isActive: true,
    });

    return {
      success: true,
      message: 'Trial subscription activated successfully',
    };
  }

  @Post('increment-usage')
  @ApiOperation({ summary: '增加AI调用使用次数' })
  async incrementUsage(
    @Request() req: ExpressRequest,
    @Body() body: { count?: number }
  ): Promise<{ success: boolean; remainingCalls: number }> {
    const userId = (req.user as any).id;
    return this.subscriptionService.incrementAiCallUsage(userId, body.count || 1);
  }

  // 仅在开发环境可用的测试接口
  @Post('test/set-time')
  async setTestTime(@Body() body: { time: string }) {
    if (process.env.NODE_ENV === 'production') {
      throw new HttpException('测试接口在生产环境不可用', HttpStatus.FORBIDDEN);
    }
    this.subscriptionService.setTestTime(new Date(body.time));
    return { message: '测试时间设置成功' };
  }

  @Post('test/clear-time')
  async clearTestTime() {
    if (process.env.NODE_ENV === 'production') {
      throw new HttpException('测试接口在生产环境不可用', HttpStatus.FORBIDDEN);
    }
    this.subscriptionService.clearTestTime();
    return { message: '测试时间已清除' };
  }
} 